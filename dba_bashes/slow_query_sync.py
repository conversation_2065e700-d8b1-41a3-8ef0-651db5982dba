#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
慢查询同步脚本 - 独立版本
不依赖公共模块，包含所有必要的方法
"""

import json
import sys
import os
import configparser
import logging
import pymysql
from datetime import datetime, date, timedelta
from typing import List, Dict, Tuple
import time
import math
import re


def setup_logging(script_name: str = 'slow_query_sync') -> logging.Logger:
    """设置日志"""
    logger = logging.getLogger(script_name)
    logger.setLevel(logging.INFO)
    
    # 避免重复添加handler
    if not logger.handlers:
        # 控制台输出
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    return logger


def load_config() -> Dict:
    """加载配置文件"""
    config_file = os.path.join(os.path.dirname(__file__), 'config.ini')
    
    if not os.path.exists(config_file):
        print(f"配置文件不存在: {config_file}")
        return {}
    
    try:
        config = configparser.ConfigParser()
        config.read(config_file, encoding='utf-8')
        
        result = {}
        
        # 结果数据库配置
        if config.has_section('result_database'):
            result['result_db_config'] = {
                'host': config.get('result_database', 'host'),
                'port': config.getint('result_database', 'port'),
                'username': config.get('result_database', 'username'),
                'password': config.get('result_database', 'password'),
                'database': config.get('result_database', 'database')
            }
        
        # 阿里云配置
        if config.has_section('aliyun'):
            result['alicloud_config'] = {
                'access_key_id': config.get('aliyun', 'access_key_id'),
                'access_key_secret': config.get('aliyun', 'access_key_secret'),
                'region_id': config.get('aliyun', 'region_id')
            }
        
        # 数据库连接配置
        if config.has_section('database'):
            result['database_config'] = {
                'rds_username': config.get('database', 'rds_username', fallback=''),
                'rds_password': config.get('database', 'rds_password', fallback=''),
                'polardb_username': config.get('database', 'polardb_username', fallback=''),
                'polardb_password': config.get('database', 'polardb_password', fallback='')
            }
        
        return result
        
    except Exception as e:
        print(f"解析配置文件失败: {e}")
        return {}


def parse_command_line_args() -> Tuple[str, List[str], str]:
    """解析命令行参数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='慢查询同步脚本')
    parser.add_argument('start_time', nargs='?', help='开始时间，格式：YYYY-MM-DD HH:MM:SS')
    parser.add_argument('--instances', '-i', help='实例ID列表，用逗号分隔')
    parser.add_argument('--account-tag', '-a', help='指定阿里云账号标签')
    
    args = parser.parse_args()
    
    start_time = args.start_time
    instance_ids = args.instances.split(',') if args.instances else None
    account_tag = args.account_tag
    
    return start_time, instance_ids, account_tag


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_config: Dict):
        self.db_config = db_config
        self.logger = setup_logging('database_manager')
    
    def connect_to_database(self):
        """连接到结果数据库"""
        try:
            connection = pymysql.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['username'],
                password=self.db_config['password'],
                database=self.db_config['database'],
                charset='utf8mb4',
                autocommit=False
            )
            return connection
        except Exception as e:
            self.logger.error(f"连接数据库失败: {e}")
            return None
    
    def get_running_instances(self, connection, instance_ids: List[str] = None) -> List[Dict]:
        """获取运行中的RDS实例列表"""
        try:
            with connection.cursor() as cursor:
                if instance_ids:
                    placeholders = ','.join(['%s'] * len(instance_ids))
                    sql = f"""
                    SELECT r.instance_id, r.instance_name, r.engine, r.engine_version, 
                           r.region_id, r.zone_id, r.status, r.instance_type, r.instance_class,
                           r.connection_string, r.port, r.description, IFNULL(s.cpu_cores, 1) as cpu_cores
                    FROM rds_manager_rdsinstance r 
                    LEFT JOIN rds_manager_rdsinstancespec s ON r.instance_class = s.instance_class
                    WHERE r.status = 'Running' 
                    AND r.instance_id IN ({placeholders})
                    ORDER BY r.instance_id
                    """
                    cursor.execute(sql, instance_ids)
                else:
                    sql = """
                    SELECT r.instance_id, r.instance_name, r.engine, r.engine_version, 
                           r.region_id, r.zone_id, r.status, r.instance_type, r.instance_class,
                           r.connection_string, r.port, r.description, IFNULL(s.cpu_cores, 1) as cpu_cores
                    FROM rds_manager_rdsinstance r 
                    LEFT JOIN rds_manager_rdsinstancespec s ON r.instance_class = s.instance_class
                    WHERE r.status = 'Running'
                    ORDER BY r.instance_id
                    """
                    cursor.execute(sql)
                
                instances = []
                for row in cursor.fetchall():
                    instances.append({
                        'instance_id': row[0],
                        'instance_name': row[1],
                        'engine': row[2],
                        'engine_version': row[3],
                        'region_id': row[4],
                        'zone_id': row[5],
                        'status': row[6],
                        'instance_type': row[7],
                        'instance_class': row[8],
                        'connection_string': row[9],
                        'port': row[10],
                        'description': row[11] or '',
                        'cpu_cores': row[12] or 1
                    })
                
                self.logger.info(f"从数据库获取运行中的实例: {len(instances)} 个")
                return instances
                
        except Exception as e:
            self.logger.error(f"从数据库获取运行中实例失败: {e}")
            return []


class AliCloudManager:
    """阿里云管理器"""
    
    def __init__(self, alicloud_config: Dict):
        self.alicloud_config = alicloud_config
        self.alicloud_client = None
        self.logger = setup_logging('alicloud_manager')
    
    def init_alicloud_client(self) -> bool:
        """初始化阿里云客户端"""
        try:
            from aliyunsdkcore.client import AcsClient
            
            self.alicloud_client = AcsClient(
                self.alicloud_config['access_key_id'],
                self.alicloud_config['access_key_secret'],
                self.alicloud_config['region_id']
            )
            
            self.logger.info("阿里云客户端初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"初始化阿里云客户端失败: {e}")
            return False
    
    def format_time_for_rds_api(self, time_str: str) -> str:
        """格式化时间为RDS API格式"""
        try:
            dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
            return dt.strftime('%Y-%m-%dT%H:%MZ')
        except Exception as e:
            self.logger.error(f"时间格式转换失败: {e}")
            return time_str
    
    def format_time_for_polardb_api(self, time_str: str) -> str:
        """格式化时间为PolarDB API格式"""
        try:
            dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
            return dt.strftime('%Y-%m-%dZ')
        except Exception as e:
            self.logger.error(f"时间格式转换失败: {e}")
            return time_str

    def get_slow_query_logs(self, instance_id: str, start_time: str, end_time: str) -> List[Dict]:
        """从阿里云RDS API获取慢查询日志"""
        try:
            from aliyunsdkrds.request.v20140815.DescribeSlowLogsRequest import DescribeSlowLogsRequest

            # 转换时间格式
            formatted_start_time = self.format_time_for_polardb_api(start_time)
            formatted_end_time = self.format_time_for_polardb_api(end_time)

            self.logger.info(f"实例 {instance_id} 获取慢查询日志: {formatted_start_time} - {formatted_end_time}")

            all_slow_logs = []
            page_number = 1
            page_size = 100

            while True:
                request = DescribeSlowLogsRequest()
                request.set_DBInstanceId(instance_id)
                request.set_StartTime(formatted_start_time)
                request.set_EndTime(formatted_end_time)
                request.set_PageSize(page_size)
                request.set_PageNumber(page_number)

                response = self.alicloud_client.do_action_with_exception(request)
                response_data = json.loads(response.decode('utf-8'))

                current_page_logs = []
                if response_data and 'Items' in response_data:
                    items = response_data['Items']
                    if 'SQLSlowLog' in items:
                        current_page_logs = items['SQLSlowLog']

                # 直接返回原始API数据，保持所有字段
                all_slow_logs.extend(current_page_logs)

                # 检查是否还有更多页面
                total_record_count = response_data.get('TotalRecordCount', 0)
                if len(all_slow_logs) >= total_record_count or len(current_page_logs) < page_size:
                    break

                page_number += 1
                self.logger.debug(f"获取第 {page_number} 页慢查询日志...")

            self.logger.info(f"从阿里云获取RDS慢查询日志: 实例 {instance_id}, 日志数量 {len(all_slow_logs)}")
            return all_slow_logs

        except Exception as e:
            self.logger.error(f"从阿里云获取慢查询日志失败: 实例 {instance_id}, 错误: {e}")
            raise

    def get_polardb_slow_query_logs(self, cluster_id: str, start_time: str, end_time: str) -> List[Dict]:
        """从阿里云PolarDB API获取慢查询日志"""
        try:
            from aliyunsdkpolardb.request.v20170801.DescribeSlowLogsRequest import DescribeSlowLogsRequest

            # 转换时间格式 - PolarDB使用yyyy-MM-ddZ格式
            formatted_start_time = self.format_time_for_polardb_api(start_time)
            formatted_end_time = self.format_time_for_polardb_api(end_time)

            self.logger.info(f"实例 {cluster_id} 获取慢查询日志: {formatted_start_time} - {formatted_end_time}")

            all_slow_logs = []
            page_number = 1
            page_size = 100

            while True:
                # 创建请求对象
                request = DescribeSlowLogsRequest()
                request.set_DBClusterId(cluster_id)
                request.set_StartTime(formatted_start_time)
                request.set_EndTime(formatted_end_time)
                request.set_PageSize(page_size)
                request.set_PageNumber(page_number)

                # 执行API调用
                response = self.alicloud_client.do_action_with_exception(request)
                result = json.loads(response)

                # 解析响应数据
                if 'Items' in result and 'SQLSlowLog' in result['Items']:
                    slow_logs = result['Items']['SQLSlowLog']
                    if not slow_logs:
                        break

                    # 直接返回原始API数据，保持所有字段（包括SQLHASH等）
                    all_slow_logs.extend(slow_logs)

                    # 检查是否还有更多页面
                    total_count = result.get('TotalRecordCount', 0)
                    if len(all_slow_logs) >= total_count or len(slow_logs) < page_size:
                        break

                    page_number += 1
                else:
                    break

            self.logger.info(f"从阿里云获取PolarDB慢查询日志: 实例 {cluster_id}, 日志数量 {len(all_slow_logs)}")
            return all_slow_logs

        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"从阿里云获取慢查询日志失败: 实例 {cluster_id}, 错误: {error_msg}")

            # 如果是实例不在此region的错误，抛出特定异常
            if "InvalidDBClusterId.NotFound" in error_msg or "not found" in error_msg.lower():
                raise Exception(f"InvalidDBClusterId.NotFound: {error_msg}")

            return []


class SlowQueryScoreManager:
    """慢查询评分管理器"""

    def __init__(self, db_config: Dict):
        self.db_config = db_config
        self.logger = setup_logging('slow_query_score')

    def get_score_config(self, connection):
        """获取评分配置 - 使用is_active=1的配置"""
        try:
            with connection.cursor() as cursor:
                # 获取激活的配置，使用具体字段名避免索引错误
                cursor.execute("""
                    SELECT name, description, is_active,
                           query_count_min, query_count_max, query_count_weight, query_count_curve,
                           query_time_min, query_time_max, query_time_weight, query_time_curve,
                           scan_rows_min, scan_rows_max, scan_rows_weight, scan_rows_curve,
                           affected_rows_min, affected_rows_max, affected_rows_weight, affected_rows_curve,
                           lock_time_min, lock_time_max, lock_time_weight, lock_time_curve,
                           polynomial_a, polynomial_b, polynomial_c, polynomial_d, polynomial_e, polynomial_f,
                           config_type
                    FROM rds_manager_slowqueryscoreconfig
                    WHERE config_type = 'slowlog' and is_active = 1
                    ORDER BY created_at DESC
                    LIMIT 1
                """)
                config_row = cursor.fetchone()

                if config_row:
                    # 根据查询字段顺序解析配置
                    config = {
                        'query_count_min': int(config_row[3]),
                        'query_count_max': int(config_row[4]),
                        'query_count_weight': float(config_row[5]),
                        'query_count_curve': str(config_row[6]),
                        'query_time_min': int(config_row[7]),
                        'query_time_max': int(config_row[8]),
                        'query_time_weight': float(config_row[9]),
                        'query_time_curve': str(config_row[10]),
                        'scan_rows_min': int(config_row[11]),
                        'scan_rows_max': int(config_row[12]),
                        'scan_rows_weight': float(config_row[13]),
                        'scan_rows_curve': str(config_row[14]),
                        'affected_rows_min': int(config_row[15]),
                        'affected_rows_max': int(config_row[16]),
                        'affected_rows_weight': float(config_row[17]),
                        'affected_rows_curve': str(config_row[18]),
                        'lock_time_min': int(config_row[19]),
                        'lock_time_max': int(config_row[20]),
                        'lock_time_weight': float(config_row[21]),
                        'lock_time_curve': str(config_row[22]),
                        'polynomial_a': float(config_row[23]),
                        'polynomial_b': float(config_row[24]),
                        'polynomial_c': float(config_row[25]),
                        'polynomial_d': float(config_row[26]),
                        'polynomial_e': float(config_row[27]),
                        'polynomial_f': float(config_row[28]),
                    }

                    self.logger.info(f"获取到评分配置: {config_row[0]}")
                    return config
                else:
                    self.logger.warning("未找到激活的评分配置，使用默认值")
                    return None

        except Exception as e:
            self.logger.error(f"获取评分配置失败: {e}")
            return None

    def calculate_score(self, value, min_val, max_val, curve_type, config=None):
        """根据曲线类型计算得分"""
        # 超出范围处理
        if value <= min_val:
            return 0
        if value >= max_val:
            return 1

        # 标准化到0-1区间
        normalized = (value - min_val) / (max_val - min_val)

        # 根据曲线类型计算得分
        if curve_type == 'linear':
            return normalized
        elif curve_type == 'exponential':
            return (math.exp(normalized) - 1) / (math.e - 1)
        elif curve_type == 'logarithmic':
            return math.log(1 + normalized) / math.log(2)
        elif curve_type == 'sine':
            return math.sin(normalized * math.pi / 2)
        elif curve_type == 'polynomial' and config:
            # 多项式曲线
            a = config.get('polynomial_a', 0.0)
            b = config.get('polynomial_b', 0.02)
            c = config.get('polynomial_c', 0.001)
            d = config.get('polynomial_d', 0.0005)
            e = config.get('polynomial_e', 0.00001)
            f = config.get('polynomial_f', 0.000001)
            x = normalized * 20
            return min(1.0, a + b*x + c*x**2 + d*x**3 + e*x**4 + f*x**5)
        else:
            return normalized

    def score_slow_query(self, slow_query: Dict, cpu_cores: int = 1, config: Dict = None) -> Dict:
        """对单个慢查询进行评分"""
        try:
            # 提取慢查询数据
            sql_count = slow_query.get('sql_count', 0)
            max_exe_time = slow_query.get('max_exe_time', 0)  # 毫秒
            parse_row_count = slow_query.get('parse_row_count', 0)
            return_row_count = slow_query.get('return_row_count', 0)
            max_lock_time = slow_query.get('max_lock_time', 0)  # 毫秒

            if config:
                # 使用配置中的参数
                query_count_min = config.get('query_count_min', 5)
                query_count_max = config.get('query_count_max', 500)
                query_count_weight = config.get('query_count_weight', 0.3)
                query_count_curve = config.get('query_count_curve', 'linear')

                query_time_min = config.get('query_time_min', 0)
                query_time_max = config.get('query_time_max', 10000)
                query_time_weight = config.get('query_time_weight', 0.2)
                query_time_curve = config.get('query_time_curve', 'sine')

                scan_rows_min = config.get('scan_rows_min', 1)
                scan_rows_max = config.get('scan_rows_max', 1000000)
                scan_rows_weight = config.get('scan_rows_weight', 0.4)
                scan_rows_curve = config.get('scan_rows_curve', 'polynomial')

                affected_rows_min = config.get('affected_rows_min', 0)
                affected_rows_max = config.get('affected_rows_max', 500)
                affected_rows_weight = config.get('affected_rows_weight', 0.05)
                affected_rows_curve = config.get('affected_rows_curve', 'linear')

                lock_time_min = config.get('lock_time_min', 0)
                lock_time_max = config.get('lock_time_max', 1000)
                lock_time_weight = config.get('lock_time_weight', 0.05)
                lock_time_curve = config.get('lock_time_curve', 'exponential')
            else:
                # 默认配置
                query_count_min, query_count_max, query_count_weight, query_count_curve = 5, 500, 0.3, 'linear'
                query_time_min, query_time_max, query_time_weight, query_time_curve = 0, 10000, 0.2, 'sine'
                scan_rows_min, scan_rows_max, scan_rows_weight, scan_rows_curve = 1, 1000000, 0.4, 'polynomial'
                affected_rows_min, affected_rows_max, affected_rows_weight, affected_rows_curve = 0, 500, 0.05, 'linear'
                lock_time_min, lock_time_max, lock_time_weight, lock_time_curve = 0, 1000, 0.05, 'exponential'

            # 计算指标值
            query_count = sql_count
            scan_rows = parse_row_count  # 最大扫描行数
            affected_rows = int(return_row_count)

            # 计算各项得分比例(0-1之间)
            query_count_score_raw = self.calculate_score(
                query_count,
                query_count_min, query_count_max, query_count_curve, config
            )
            query_time_score_raw = self.calculate_score(
                max_exe_time, query_time_min, query_time_max, query_time_curve, config
            )
            scan_rows_score_raw = self.calculate_score(
                scan_rows, scan_rows_min, scan_rows_max, scan_rows_curve, config
            )
            affected_rows_score_raw = self.calculate_score(
                affected_rows, affected_rows_min, affected_rows_max, affected_rows_curve, config
            )
            lock_time_score_raw = self.calculate_score(
                max_lock_time, lock_time_min, lock_time_max, lock_time_curve, config
            )

            # 应用权重并计算最终得分
            query_count_score = int(query_count_score_raw * query_count_weight * 100)
            query_time_score = int(query_time_score_raw * query_time_weight * 100)
            scan_rows_score = int(scan_rows_score_raw * scan_rows_weight * 100)
            affected_rows_score = int(affected_rows_score_raw * affected_rows_weight * 100)
            lock_time_score = int(lock_time_score_raw * lock_time_weight * 100)

            # 计算总风险评分
            risk_score = query_count_score + query_time_score + scan_rows_score + affected_rows_score + lock_time_score

            # 对于执行次数低于24次的查询，应用惩罚系数
            if sql_count < 24:
                risk_score = int(risk_score * 0.05)
                query_count_score = int(query_count_score * 0.05)
                query_time_score = int(query_time_score * 0.05)
                scan_rows_score = int(scan_rows_score * 0.05)
                affected_rows_score = int(affected_rows_score * 0.05)
                lock_time_score = int(lock_time_score * 0.05)

            return {
                'risk_score': min(risk_score, 100),  # 限制最大值为100
                'query_count_score': query_count_score,
                'query_time_score': query_time_score,
                'scan_rows_score': scan_rows_score,
                'affected_rows_score': affected_rows_score,
                'lock_time_score': lock_time_score,
                'analysis_result': f"风险评分: {risk_score}分",
                'optimization_suggestion': "建议优化查询性能" if risk_score > 50 else "查询性能良好"
            }

        except Exception as e:
            self.logger.error(f"评分计算失败: {e}")
            return {
                'risk_score': 0,
                'query_count_score': 0,
                'query_time_score': 0,
                'scan_rows_score': 0,
                'affected_rows_score': 0,
                'lock_time_score': 0,
                'analysis_result': "评分计算失败",
                'optimization_suggestion': "无法提供建议"
            }


class SlowQuerySyncManager:
    """慢查询同步管理器"""

    def __init__(self, result_db_config: Dict, alicloud_config: Dict):
        self.result_db_config = result_db_config
        self.alicloud_config = alicloud_config
        self.logger = setup_logging('slow_query_sync')

        # 创建管理器
        self.db_manager = DatabaseManager(result_db_config)
        self.alicloud_manager = AliCloudManager(alicloud_config)
        self.score_manager = SlowQueryScoreManager(result_db_config)

        # 统计信息
        self.stats = {
            'sync_start_time': None,
            'sync_end_time': None,
            'total_instances': 0,
            'successful_instances': 0,
            'failed_instances': 0,
            'total_slow_queries': 0,
            'total_queries_created': 0,
            'total_queries_updated': 0,
            'total_queries_scored': 0,
            'processing_time': 0,
            'errors': 0,
            'error_details': [],
            'instance_details': []
        }

    def get_region_id_by_pk(self, connection, region_pk):
        """根据 region 表主键 ID 查找实际的 region_id 字符串"""
        if not region_pk:
            return None
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT region_id FROM rds_manager_alicloudregion WHERE id = %s", (region_pk,))
                row = cursor.fetchone()
                if row:
                    return row[0]
                else:
                    self.logger.warning(f"未找到主键为 {region_pk} 的region记录")
                    return None
        except Exception as e:
            self.logger.error(f"查询region_id失败: {e}")
            return None

    def get_slow_query_logs(self, instance_id: str, start_time: str, end_time: str, instance_region: str = None, instance_type: str = None) -> List[Dict]:
        """从阿里云API获取慢查询日志，支持RDS和PolarDB，支持多region"""
        # 获取region列表
        regions = self.alicloud_config.get('region_ids', [self.alicloud_config.get('region_id', 'cn-hangzhou')])

        # 如果实例指定了region，优先使用
        if instance_region:
            regions = [instance_region]

        for region_id in regions:
            try:
                # 为当前region创建新的管理器
                region_config = self.alicloud_config.copy()
                region_config['region_id'] = region_id

                region_manager = AliCloudManager(region_config)
                if not region_manager.init_alicloud_client():
                    self.logger.error(f"region {region_id} 的阿里云客户端初始化失败，跳过")
                    continue

                # 根据实例类型调用不同的API
                self.logger.info(f"实例 {instance_id} 类型判断: instance_type='{instance_type}', 是否包含polardb: {'polardb' in instance_type.lower()}")

                if instance_type and 'polardb' in instance_type.lower():
                    # PolarDB实例
                    self.logger.info(f"识别为PolarDB实例: {instance_id}")
                    slow_logs = region_manager.get_polardb_slow_query_logs(instance_id, start_time, end_time)
                    self.logger.info(f"从region {region_id} 获取PolarDB实例 {instance_id} 慢查询日志: {len(slow_logs)} 条")
                else:
                    # RDS实例（默认）
                    self.logger.info(f"识别为RDS实例: {instance_id}")
                    slow_logs = region_manager.get_slow_query_logs(instance_id, start_time, end_time)
                    self.logger.info(f"从region {region_id} 获取RDS实例 {instance_id} 慢查询日志: {len(slow_logs)} 条")
                return slow_logs

            except Exception as e:
                error_msg = str(e)
                # 如果是实例不在此region的错误，尝试下一个region
                if ("InvalidDBInstanceId.NotFound" in error_msg or
                    "InvalidDBClusterId.NotFound" in error_msg or
                    "not found" in error_msg.lower()):
                    self.logger.debug(f"实例 {instance_id} 不在region {region_id}，尝试下一个region")
                    continue
                else:
                    self.logger.error(f"从region {region_id} 获取慢查询日志失败: {e}")
                    if instance_region:  # 如果指定了region，直接抛出异常
                        raise
                    continue

        # 如果所有region都失败了
        self.logger.error(f"在所有regions中都无法获取实例 {instance_id} 的慢查询日志")
        return []

    def save_slow_queries(self, connection, instance_id: str, slow_logs: List[Dict], sync_date: date, cpu_cores: int = 1) -> Tuple[int, int]:
        """保存慢查询数据到数据库并进行评分"""
        try:
            created_count = 0
            updated_count = 0
            current_time = datetime.now()

            # 获取评分配置
            score_config = self.score_manager.get_score_config(connection)

            with connection.cursor() as cursor:
                for log in slow_logs:
                    try:
                        # 提取记录数据（使用API原始字段名）
                        sql_text = log.get('SQLText', '')
                        db_name = log.get('DBName', '')

                        if not sql_text or not db_name:
                            self.logger.debug(f"跳过记录：SQL文本或数据库名为空 - SQLText: {bool(sql_text)}, DBName: {bool(db_name)}")
                            continue

                        # 使用API返回的SQLHASH，不要自己计算
                        sql_hash = log.get('SQLHASH', '')
                        if not sql_hash:
                            # 如果API没有返回SQLHASH，则跳过这条记录
                            self.logger.warning(f"慢查询记录缺少SQLHASH，跳过: {sql_text[:50]}...")
                            self.logger.debug(f"完整记录内容: {log}")
                            continue

                        # 检查是否已存在
                        check_sql = """
                        SELECT id FROM rds_manager_slowquerylog
                        WHERE sql_hash = %s AND instance_id = %s AND timezone = %s
                        """
                        cursor.execute(check_sql, (sql_hash, instance_id, sync_date))
                        existing = cursor.fetchone()

                        # 准备数据（根据API文档使用正确的字段名）
                        # 执行次数：RDS使用MySQLTotalExecutionCounts，PolarDB使用TotalExecutionCounts
                        sql_count = int(log.get('MySQLTotalExecutionCounts', 0) or log.get('TotalExecutionCounts', 0) or 1)

                        # 扫描行数：使用ParseTotalRowCounts（解析的SQL行数最大值）
                        parse_row_count = int(log.get('ParseMaxRowCount', 0))

                        # 返回行数：使用ReturnTotalRowCounts（返回的SQL行数最大值）
                        return_row_count = int(log.get('ReturnMaxRowCount', 0))

                        # 执行时长：RDS使用MaxExecutionTime，PolarDB使用MaxExecutionTime（单位：秒）
                        total_exe_time_sec = float(log.get('MaxExecutionTime', 0))
                        max_exe_time = int(total_exe_time_sec * 1000)  # 转换为毫秒

                        # 锁时间：使用MaxLockTime（单位：秒）
                        total_lock_time_sec = float(log.get('MaxLockTime', 0))
                        max_lock_time = int(total_lock_time_sec * 1000)  # 转换为毫秒

                        # 调试信息：输出解析的字段值
                        self.logger.debug(f"解析字段值 - SQL哈希: {sql_hash}, 执行次数: {sql_count}, "
                                        f"执行时长: {max_exe_time}ms, 扫描行数: {parse_row_count}, "
                                        f"返回行数: {return_row_count}, 锁时间: {max_lock_time}ms")

                        # 准备慢查询数据用于评分
                        slow_query_data = {
                            'sql_count': sql_count,
                            'max_exe_time': max_exe_time,
                            'parse_row_count': parse_row_count,
                            'return_row_count': return_row_count,
                            'max_lock_time': max_lock_time
                        }

                        # 进行评分
                        score_result = self.score_manager.score_slow_query(slow_query_data, cpu_cores, score_config)

                        # 简单风险评级（保持兼容性）
                        risk_level = 2 if max_exe_time > 5000 else (1 if max_exe_time > 1000 else 0)

                        if existing:
                            # 更新现有记录
                            update_sql = """
                            UPDATE rds_manager_slowquerylog SET
                                database_name = %s, sql_text = %s, sql_count = %s,
                                parse_row_count = %s, return_row_count = %s,
                                max_exe_time = %s, max_lock_time = %s, risk_level = %s
                            WHERE id = %s
                            """
                            cursor.execute(update_sql, (
                                db_name, sql_text, sql_count, parse_row_count,
                                return_row_count, max_exe_time, max_lock_time,
                                risk_level, existing[0]
                            ))
                            updated_count += 1

                            # 更新或插入分析结果
                            self.save_slow_query_analysis(cursor, sql_hash, instance_id, sync_date, score_result, current_time)
                        else:
                            # 插入新记录
                            insert_sql = """
                            INSERT INTO rds_manager_slowquerylog (
                                instance_id, database_name, sql_hash, sql_count,
                                parse_row_count, return_row_count, max_exe_time,
                                max_lock_time, timezone, sql_text, risk_level, created_at
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                            """
                            cursor.execute(insert_sql, (
                                instance_id, db_name, sql_hash, sql_count,
                                parse_row_count, return_row_count, max_exe_time,
                                max_lock_time, sync_date, sql_text, risk_level, current_time
                            ))
                            created_count += 1

                            # 插入分析结果
                            self.save_slow_query_analysis(cursor, sql_hash, instance_id, sync_date, score_result, current_time)

                    except Exception as e:
                        self.logger.error(f"保存单个慢查询记录失败: {e}")
                        continue

            self.logger.info(f"保存慢查询记录完成: 实例 {instance_id}, 新增 {created_count}, 更新 {updated_count}")
            return created_count, updated_count

        except Exception as e:
            self.logger.error(f"保存慢查询记录失败: 实例 {instance_id}, 错误: {e}")
            raise

    def save_slow_query_analysis(self, cursor, sql_hash: str, instance_id: str, sync_date: date, score_result: Dict, current_time: datetime):
        """保存慢查询分析结果"""
        try:
            # 检查是否已存在分析结果
            check_sql = """
            SELECT id FROM rds_manager_slowqueryanalysis
            WHERE sql_hash = %s AND instance_id = %s AND timezone = %s
            """
            cursor.execute(check_sql, (sql_hash, instance_id, sync_date))
            existing = cursor.fetchone()

            if existing:
                # 更新现有分析结果
                update_sql = """
                UPDATE rds_manager_slowqueryanalysis SET
                    risk_score = %s, query_count_score = %s, query_time_score = %s,
                    scan_rows_score = %s, affected_rows_score = %s, lock_time_score = %s,
                    analysis_result = %s, optimization_suggestion = %s
                WHERE id = %s
                """
                cursor.execute(update_sql, (
                    score_result['risk_score'],
                    score_result['query_count_score'],
                    score_result['query_time_score'],
                    score_result['scan_rows_score'],
                    score_result['affected_rows_score'],
                    score_result['lock_time_score'],
                    score_result['analysis_result'],
                    score_result['optimization_suggestion'],
                    existing[0]
                ))
            else:
                # 插入新的分析结果
                insert_sql = """
                INSERT INTO rds_manager_slowqueryanalysis (
                    sql_hash, instance_id, timezone, table_scans, no_indexes, poor_indexes,
                    temporary_tables, filesort, analysis_result, optimization_suggestion,
                    risk_score, query_count_score, query_time_score, scan_rows_score,
                    affected_rows_score, lock_time_score, created_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(insert_sql, (
                    sql_hash, instance_id, sync_date,
                    False, False, False, False, False,  # 风险特征标识，暂时设为False
                    score_result['analysis_result'],
                    score_result['optimization_suggestion'],
                    score_result['risk_score'],
                    score_result['query_count_score'],
                    score_result['query_time_score'],
                    score_result['scan_rows_score'],
                    score_result['affected_rows_score'],
                    score_result['lock_time_score'],
                    current_time
                ))

        except Exception as e:
            self.logger.error(f"保存慢查询分析结果失败: {e}")

    def process_instance(self, instance: Dict, start_time: str, end_time: str, sync_date: date) -> Dict:
        """处理单个实例的慢查询同步"""
        instance_id = instance['instance_id']
        instance_name = instance['instance_name']

        result = {
            'instance_id': instance_id,
            'instance_name': instance_name,
            'status': 'processing',
            'slow_queries_count': 0,
            'created_count': 0,
            'updated_count': 0,
            'error': None,
            'processing_time': 0
        }

        start_processing_time = time.time()

        try:
            # 连接数据库
            connection = self.db_manager.connect_to_database()
            if not connection:
                raise Exception("无法连接到结果数据库")

            try:
                # 获取慢查询日志
                region_pk = instance.get('region_id')
                instance_region = self.get_region_id_by_pk(connection, region_pk) if region_pk else None
                instance_type = instance.get('instance_type', '')
                slow_logs = self.get_slow_query_logs(instance_id, start_time, end_time, instance_region, instance_type)

                # 获取CPU核心数
                cpu_cores = instance.get('cpu_cores', 1)

                # 保存数据并进行评分
                created_count, updated_count = self.save_slow_queries(
                    connection, instance_id, slow_logs, sync_date, cpu_cores
                )

                # 提交事务
                connection.commit()

                # 更新结果
                result['status'] = 'success'
                result['slow_queries_count'] = len(slow_logs)
                result['created_count'] = created_count
                result['updated_count'] = updated_count
                result['processing_time'] = time.time() - start_processing_time

                self.logger.info(f"实例 {instance_id} 同步成功: 慢查询数量 {len(slow_logs)}, 新增 {created_count}, 更新 {updated_count}, 已完成评分")

            finally:
                connection.close()

        except Exception as e:
            error_msg = f"同步实例 {instance_id} 慢查询失败: {str(e)}"
            self.logger.error(error_msg)
            result['status'] = 'failed'
            result['error'] = str(e)
            result['processing_time'] = time.time() - start_processing_time

            self.stats['errors'] += 1
            self.stats['error_details'].append(error_msg)

        return result

    def run(self, start_time: str = None, end_time: str = None, instance_ids: List[str] = None):
        """运行慢查询同步"""
        # 设置默认时间范围
        if not start_time:
            yesterday = datetime.now() - timedelta(days=1)
            start_time = yesterday.strftime('%Y-%m-%d 00:00:00')
        if not end_time:
            yesterday = datetime.now() - timedelta(days=1)
            end_time = yesterday.strftime('%Y-%m-%d 23:59:59')

        self.stats['sync_start_time'] = start_time
        self.stats['sync_end_time'] = end_time
        start_processing_time = time.time()

        self.logger.info(f"开始同步慢查询: 时间范围 {start_time} - {end_time}")

        # 初始化阿里云客户端
        if not self.alicloud_manager.init_alicloud_client():
            self.logger.error("初始化阿里云客户端失败，停止同步")
            return

        # 连接数据库获取实例列表
        connection = self.db_manager.connect_to_database()
        if not connection:
            self.logger.error("无法连接到数据库，停止同步")
            return

        try:
            # 获取要同步的实例
            instances = self.db_manager.get_running_instances(connection, instance_ids)

            if not instances:
                self.logger.warning("没有找到需要同步的实例")
                return

            # 从时间范围提取日期
            sync_date = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S').date()

            self.stats['total_instances'] = len(instances)

            # 处理所有实例
            for instance in instances:
                result = self.process_instance(instance, start_time, end_time, sync_date)
                self.stats['instance_details'].append(result)

                if result['status'] == 'success':
                    self.stats['successful_instances'] += 1
                    self.stats['total_slow_queries'] += result['slow_queries_count']
                    self.stats['total_queries_created'] += result['created_count']
                    self.stats['total_queries_updated'] += result['updated_count']
                    self.stats['total_queries_scored'] += result['slow_queries_count']  # 所有慢查询都进行了评分
                else:
                    self.stats['failed_instances'] += 1

            # 计算处理时间
            self.stats['processing_time'] = time.time() - start_processing_time

            self.logger.info("慢查询同步完成")
            self.output_stats()

        finally:
            connection.close()

    def output_stats(self):
        """输出统计信息"""
        self.logger.info(f"慢查询同步统计:")
        self.logger.info(f"- 同步时间范围: {self.stats['sync_start_time']} - {self.stats['sync_end_time']}")
        self.logger.info(f"- 实例总数: {self.stats['total_instances']}")
        self.logger.info(f"- 成功实例: {self.stats['successful_instances']}")
        self.logger.info(f"- 失败实例: {self.stats['failed_instances']}")
        self.logger.info(f"- 慢查询总数: {self.stats['total_slow_queries']}")
        self.logger.info(f"- 新增记录: {self.stats['total_queries_created']}")
        self.logger.info(f"- 更新记录: {self.stats['total_queries_updated']}")
        self.logger.info(f"- 评分记录: {self.stats['total_queries_scored']}")
        self.logger.info(f"- 处理耗时: {self.stats['processing_time']:.2f} 秒")

    def output_final_stats(self):
        """输出最终统计信息"""
        print(f"- 同步时间范围: {self.stats['sync_start_time']} - {self.stats['sync_end_time']}")
        print(f"- 实例总数: {self.stats['total_instances']}")
        print(f"- 成功实例: {self.stats['successful_instances']}")
        print(f"- 失败实例: {self.stats['failed_instances']}")
        print(f"- 慢查询总数: {self.stats['total_slow_queries']}")
        print(f"- 新增记录: {self.stats['total_queries_created']}")
        print(f"- 更新记录: {self.stats['total_queries_updated']}")
        print(f"- 评分记录: {self.stats['total_queries_scored']}")
        print(f"- 处理耗时: {self.stats['processing_time']:.2f} 秒")


def main():
    """主函数"""
    print("慢查询同步脚本 - 独立版本")
    print("=" * 50)

    # 加载配置
    config = load_config()

    if not config or not config.get('result_db_config') or not config.get('alicloud_config'):
        print("配置文件加载失败或缺少必要配置")
        return

    # 解析命令行参数
    start_time, instance_ids, account_tag = parse_command_line_args()
    end_time = None

    # account_tag 用于多账号支持，当前版本暂未使用

    # 处理时间参数
    if start_time:
        try:
            datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
            print(f"使用指定开始时间: {start_time}")
        except ValueError:
            print(f"开始时间格式错误: {start_time}，请使用 'YYYY-MM-DD HH:MM:SS' 格式")
            return

    if len(sys.argv) > 2:
        try:
            end_time = sys.argv[2]
            datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
            print(f"使用指定结束时间: {end_time}")
        except ValueError:
            print(f"结束时间格式错误: {end_time}，请使用 'YYYY-MM-DD HH:MM:SS' 格式")
            return

    if instance_ids:
        print(f"使用指定实例列表: {instance_ids}")

    # 创建同步器
    sync_manager = SlowQuerySyncManager(
        config['result_db_config'],
        config['alicloud_config']
    )

    try:
        # 运行同步
        sync_manager.run(start_time, end_time, instance_ids)

        print(f"\n慢查询同步完成!")
        sync_manager.output_final_stats()

    except KeyboardInterrupt:
        print("用户中断操作")
    except Exception as e:
        print(f"同步过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
