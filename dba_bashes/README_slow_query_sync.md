# 慢查询同步脚本使用说明

## 概述

本项目提供了两个版本的慢查询同步脚本：

1. **简化版本** (`slow_query_sync_simplified.py`) - 依赖公共模块，代码简洁
2. **独立版本** (`slow_query_sync.py`) - 完全独立，不依赖公共模块

## 功能特性

- 支持 RDS MySQL 和 PolarDB MySQL 慢查询同步
- 支持多 Region 自动检测
- 使用阿里云官方 API 获取慢查询统计数据
- 正确处理 API 返回的字段（SQLHASH、执行次数、执行时长等）
- 支持增量同步（新增/更新记录）
- 详细的日志输出和统计信息

## 配置文件

确保 `config.ini` 文件包含以下配置：

```ini
[aliyun]
access_key_id = YOUR_ACCESS_KEY_ID
access_key_secret = YOUR_ACCESS_KEY_SECRET
region_id = cn-hangzhou

[result_database]
host = *************
port = 3306
username = root
password = your_password
database = dba_0331

[database]
rds_username = your_rds_username
rds_password = your_rds_password
polardb_username = your_polardb_username
polardb_password = your_polardb_password
```

## 使用方法

### 1. 简化版本（推荐在开发环境使用）

```bash
# 同步昨天的慢查询
python slow_query_sync_simplified.py

# 指定时间范围
python slow_query_sync_simplified.py "2024-01-01 00:00:00" "2024-01-01 23:59:59"

# 指定特定实例
python slow_query_sync_simplified.py "2024-01-01 00:00:00" --instances "rm-xxx,pc-yyy"
```

### 2. 独立版本（推荐在生产环境使用）

```bash
# 同步昨天的慢查询
python slow_query_sync.py

# 指定时间范围
python slow_query_sync.py "2024-01-01 00:00:00" "2024-01-01 23:59:59"

# 指定特定实例
python slow_query_sync.py "2024-01-01 00:00:00" --instances "rm-xxx,pc-yyy"
```

## 测试脚本

### 测试独立版本功能

```bash
python test_independent_sync.py
```

### 调试 PolarDB 慢查询

```bash
python debug_polardb_slow_query.py
```

### 测试 API 字段返回

```bash
python test_slow_query_api.py
```

## 主要修复内容

### 1. SQLHASH 字段处理
- **修复前**: 自己计算 MD5 哈希值
- **修复后**: 直接使用 API 返回的 `SQLHASH` 字段

### 2. 执行次数字段
- **RDS**: 使用 `MySQLTotalExecutionCounts`
- **PolarDB**: 使用 `TotalExecutionCounts`

### 3. 执行时长字段
- **RDS**: 使用 `MySQLTotalExecutionTimes`（秒）
- **PolarDB**: 使用 `TotalExecutionTimes`（秒）
- 转换为毫秒存储

### 4. 扫描行数字段
- 统一使用 `ParseTotalRowCounts`（解析的SQL行数总值）

### 5. 返回行数字段
- 统一使用 `ReturnTotalRowCounts`（返回的SQL行数总值）

### 6. 锁时间字段
- 统一使用 `TotalLockTimes`（总锁时间，秒）
- 转换为毫秒存储

## 故障排除

### 1. PolarDB 数据不写入问题

检查以下几点：
- 实例类型识别是否正确（包含 "polardb"）
- API 是否返回 `SQLHASH` 字段
- 字段值是否为空
- Region 配置是否正确

### 2. API 调用失败

- 检查阿里云 AccessKey 权限
- 确认实例所在 Region
- 检查网络连接

### 3. 数据库连接问题

- 确认数据库配置正确
- 检查网络连通性
- 验证用户权限

## 日志级别

脚本支持详细的日志输出：
- `INFO`: 基本操作信息
- `DEBUG`: 详细调试信息
- `WARNING`: 警告信息
- `ERROR`: 错误信息

## 性能优化

- 使用分页获取大量数据
- 支持批量插入/更新
- 自动重试机制
- 连接池管理

## 注意事项

1. 慢查询统计数据可能有 6-8 小时延迟
2. 建议在低峰期运行同步任务
3. 定期清理历史数据以保持性能
4. 监控脚本执行状态和错误日志
