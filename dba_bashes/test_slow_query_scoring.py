#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试慢查询评分功能
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from slow_query_sync import SlowQueryScoreManager, DatabaseManager, load_config

def test_scoring():
    """测试评分功能"""
    print("测试慢查询评分功能")
    print("=" * 50)
    
    # 加载配置
    config = load_config()
    if not config or not config.get('result_db_config'):
        print("配置文件加载失败或缺少数据库配置")
        return
    
    # 创建评分管理器
    score_manager = SlowQueryScoreManager(config['result_db_config'])
    db_manager = DatabaseManager(config['result_db_config'])
    
    # 连接数据库
    connection = db_manager.connect_to_database()
    if not connection:
        print("无法连接到数据库")
        return
    
    try:
        # 获取评分配置
        score_config = score_manager.get_score_config(connection)
        if score_config:
            print(f"✓ 成功获取评分配置")
            print(f"  查询次数权重: {score_config['query_count_weight']}")
            print(f"  查询时间权重: {score_config['query_time_weight']}")
            print(f"  扫描行数权重: {score_config['scan_rows_weight']}")
        else:
            print("✗ 未找到评分配置，将使用默认配置")
            score_config = None
        
        # 测试数据
        test_cases = [
            {
                'name': '高风险慢查询',
                'data': {
                    'sql_count': 1000,
                    'max_exe_time': 8000,  # 8秒
                    'parse_row_count': 500000,
                    'return_row_count': 1000,
                    'max_lock_time': 500
                }
            },
            {
                'name': '中等风险慢查询',
                'data': {
                    'sql_count': 100,
                    'max_exe_time': 2000,  # 2秒
                    'parse_row_count': 10000,
                    'return_row_count': 100,
                    'max_lock_time': 100
                }
            },
            {
                'name': '低风险慢查询',
                'data': {
                    'sql_count': 50,
                    'max_exe_time': 500,  # 0.5秒
                    'parse_row_count': 1000,
                    'return_row_count': 50,
                    'max_lock_time': 10
                }
            },
            {
                'name': '低频查询（惩罚测试）',
                'data': {
                    'sql_count': 10,  # 低于24次
                    'max_exe_time': 5000,  # 5秒
                    'parse_row_count': 100000,
                    'return_row_count': 500,
                    'max_lock_time': 200
                }
            }
        ]
        
        print("\n测试评分结果:")
        print("-" * 80)
        
        for test_case in test_cases:
            print(f"\n{test_case['name']}:")
            result = score_manager.score_slow_query(test_case['data'], cpu_cores=4, config=score_config)
            
            print(f"  总风险评分: {result['risk_score']}")
            print(f"  查询次数评分: {result['query_count_score']}")
            print(f"  查询时间评分: {result['query_time_score']}")
            print(f"  扫描行数评分: {result['scan_rows_score']}")
            print(f"  影响行数评分: {result['affected_rows_score']}")
            print(f"  锁等待评分: {result['lock_time_score']}")
            print(f"  分析结果: {result['analysis_result']}")
            print(f"  优化建议: {result['optimization_suggestion']}")
        
        print("\n✓ 评分功能测试完成")
        
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        connection.close()

if __name__ == "__main__":
    test_scoring()
