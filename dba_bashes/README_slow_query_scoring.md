# 慢查询同步脚本评分功能说明

## 功能概述

慢查询同步脚本 (`slow_query_sync.py`) 已经增强，现在在获取慢查询数据后会自动对所有慢查询进行评分。

## 主要功能

### 1. 慢查询数据同步
- 从阿里云RDS/PolarDB API获取慢查询日志
- 支持多region和多账号配置
- 自动处理分页获取所有数据

### 2. 自动评分功能 ✨ **新增**
- 获取完慢查询数据后，自动对所有慢查询进行评分
- 使用数据库中 `is_active=1` 的评分配置模板
- 支持多种评分曲线：线性、指数、对数、正弦、多项式
- 低频查询（执行次数<24次）自动应用0.05惩罚系数

### 3. 评分指标
评分基于以下5个维度：

1. **查询次数评分** - 基于QPS（每秒查询数）
2. **查询时间评分** - 基于平均执行时间（毫秒）
3. **扫描行数评分** - 基于总扫描行数
4. **影响行数评分** - 基于返回行数
5. **锁等待评分** - 基于锁等待时间（毫秒）

### 4. 数据存储
- 慢查询数据存储到 `rds_manager_slowquerylog` 表
- 评分结果存储到 `rds_manager_slowqueryanalysis` 表
- 自动处理新增和更新记录

## 使用方法

### 基本用法
```bash
# 同步昨天的慢查询数据并进行评分
python slow_query_sync.py

# 同步指定时间范围的数据
python slow_query_sync.py "2025-07-16 00:00:00" "2025-07-16 23:59:59"

# 同步指定实例的数据
python slow_query_sync.py "2025-07-16 00:00:00" --instances "rm-instance1,rm-instance2"
```

### 配置要求

1. **数据库配置** - `config.ini` 中需要配置：
   ```ini
   [result_database]
   host = your_db_host
   port = 3306
   username = your_username
   password = your_password
   database = your_database
   ```

2. **阿里云配置** - 需要配置阿里云访问密钥：
   ```ini
   [aliyun]
   access_key_id = your_access_key
   access_key_secret = your_secret
   region_id = cn-hangzhou
   ```

3. **评分配置** - 数据库中需要有激活的评分配置：
   - 表：`rds_manager_slowqueryscoreconfig`
   - 条件：`is_active = 1`

## 输出示例

```
慢查询同步脚本 - 独立版本
==================================================
2025-07-17 15:30:00,000 - slow_query_sync - INFO - 开始同步慢查询: 时间范围 2025-07-16 00:00:00 - 2025-07-16 23:59:59
2025-07-17 15:30:01,000 - slow_query_sync - INFO - 获取到评分配置: audit_score
2025-07-17 15:30:02,000 - slow_query_sync - INFO - 实例 rm-xxx 同步成功: 慢查询数量 150, 新增 120, 更新 30, 已完成评分

慢查询同步统计:
- 同步时间范围: 2025-07-16 00:00:00 - 2025-07-16 23:59:59
- 实例总数: 5
- 成功实例: 5
- 失败实例: 0
- 慢查询总数: 750
- 新增记录: 600
- 更新记录: 150
- 评分记录: 750
- 处理耗时: 45.67 秒
```

## 测试功能

提供了独立的测试脚本来验证评分功能：

```bash
python test_slow_query_scoring.py
```

测试脚本会：
1. 连接数据库获取评分配置
2. 使用不同风险级别的测试数据进行评分
3. 验证低频查询惩罚机制
4. 输出详细的评分结果

## 注意事项

1. **配置依赖** - 确保数据库中有激活的评分配置，否则使用默认配置
2. **性能考虑** - 评分计算会增加一定的处理时间，但影响较小
3. **数据一致性** - 评分结果与慢查询数据保持一致的时间范围
4. **错误处理** - 评分失败不会影响慢查询数据的正常同步

## 技术实现

- **评分管理器** - `SlowQueryScoreManager` 类负责评分逻辑
- **配置获取** - 自动从数据库获取激活的评分配置
- **曲线计算** - 支持多种数学曲线进行评分计算
- **批量处理** - 高效处理大量慢查询数据的评分
- **事务安全** - 确保数据一致性和完整性

## 更新日志

- **v2.0** - 新增自动评分功能
- **v1.0** - 基础慢查询同步功能
