#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试独立版本慢查询同步脚本
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入独立版本的模块
from slow_query_sync import SlowQuerySyncManager, load_config


def test_independent_sync():
    """测试独立版本的慢查询同步"""
    print("=" * 60)
    print("测试独立版本慢查询同步")
    print("=" * 60)
    
    # 加载配置
    config = load_config()
    if not config or not config.get('result_db_config') or not config.get('alicloud_config'):
        print("配置文件加载失败或缺少必要配置")
        return
    
    # 创建同步管理器
    sync_manager = SlowQuerySyncManager(
        config['result_db_config'],
        config['alicloud_config']
    )
    
    # 测试时间范围（最近1天）
    end_time = datetime.now()
    start_time = end_time - timedelta(days=1)
    
    start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
    end_time_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
    
    print(f"测试时间范围: {start_time_str} - {end_time_str}")
    
    try:
        # 测试数据库连接
        print("\n1. 测试数据库连接...")
        connection = sync_manager.db_manager.connect_to_database()
        if connection:
            print("✓ 数据库连接成功")
            
            # 获取实例列表
            print("\n2. 获取实例列表...")
            instances = sync_manager.db_manager.get_running_instances(connection)
            print(f"找到 {len(instances)} 个运行中的实例")
            
            # 显示前5个实例
            for i, instance in enumerate(instances[:5]):
                print(f"  {i+1}. {instance['instance_id']} ({instance['instance_type']}) - {instance['instance_name']}")
            
            connection.close()
        else:
            print("❌ 数据库连接失败")
            return
        
        # 测试阿里云客户端初始化
        print("\n3. 测试阿里云客户端初始化...")
        if sync_manager.alicloud_manager.init_alicloud_client():
            print("✓ 阿里云客户端初始化成功")
        else:
            print("❌ 阿里云客户端初始化失败")
            return
        
        # 选择一个PolarDB实例进行测试
        print("\n4. 查找PolarDB实例进行测试...")
        connection = sync_manager.db_manager.connect_to_database()
        if connection:
            with connection.cursor() as cursor:
                sql = """
                SELECT instance_id, instance_name, instance_type, region_id
                FROM rds_manager_rdsinstance 
                WHERE status = 'Running' 
                AND instance_type LIKE '%PolarDB%'
                ORDER BY instance_id
                LIMIT 1
                """
                cursor.execute(sql)
                polardb_instance = cursor.fetchone()
                
                if polardb_instance:
                    test_instance = {
                        'instance_id': polardb_instance[0],
                        'instance_name': polardb_instance[1],
                        'instance_type': polardb_instance[2],
                        'region_id': polardb_instance[3]
                    }
                    
                    print(f"找到PolarDB测试实例: {test_instance['instance_id']}")
                    
                    # 测试单个实例同步
                    print("\n5. 测试单个实例同步...")
                    result = sync_manager.process_instance(
                        test_instance,
                        start_time_str,
                        end_time_str,
                        datetime.now().date()
                    )
                    
                    print(f"同步结果:")
                    print(f"  状态: {result['status']}")
                    print(f"  慢查询数量: {result['slow_queries_count']}")
                    print(f"  新增记录: {result['created_count']}")
                    print(f"  更新记录: {result['updated_count']}")
                    print(f"  处理时间: {result['processing_time']:.2f}秒")
                    
                    if result['error']:
                        print(f"  错误: {result['error']}")
                    
                    # 检查数据库中的记录
                    check_sql = """
                    SELECT COUNT(*) FROM rds_manager_slowquerylog 
                    WHERE instance_id = %s AND timezone = %s
                    """
                    cursor.execute(check_sql, (test_instance['instance_id'], datetime.now().date()))
                    record_count = cursor.fetchone()[0]
                    print(f"  数据库中的记录数: {record_count}")
                    
                else:
                    print("未找到PolarDB实例，测试RDS实例...")
                    
                    # 查找RDS实例
                    sql = """
                    SELECT instance_id, instance_name, instance_type, region_id
                    FROM rds_manager_rdsinstance 
                    WHERE status = 'Running' 
                    AND instance_type LIKE '%RDS%'
                    ORDER BY instance_id
                    LIMIT 1
                    """
                    cursor.execute(sql)
                    rds_instance = cursor.fetchone()
                    
                    if rds_instance:
                        test_instance = {
                            'instance_id': rds_instance[0],
                            'instance_name': rds_instance[1],
                            'instance_type': rds_instance[2],
                            'region_id': rds_instance[3]
                        }
                        
                        print(f"找到RDS测试实例: {test_instance['instance_id']}")
                        
                        # 测试单个实例同步
                        print("\n5. 测试单个实例同步...")
                        result = sync_manager.process_instance(
                            test_instance,
                            start_time_str,
                            end_time_str,
                            datetime.now().date()
                        )
                        
                        print(f"同步结果:")
                        print(f"  状态: {result['status']}")
                        print(f"  慢查询数量: {result['slow_queries_count']}")
                        print(f"  新增记录: {result['created_count']}")
                        print(f"  更新记录: {result['updated_count']}")
                        print(f"  处理时间: {result['processing_time']:.2f}秒")
                        
                        if result['error']:
                            print(f"  错误: {result['error']}")
                    else:
                        print("未找到任何可测试的实例")
            
            connection.close()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("独立版本慢查询同步测试脚本")
    test_independent_sync()
    print("\n测试完成")


if __name__ == "__main__":
    main()
